# Как увидеть ответы в логах

## Что добавлено для просмотра ответов

Теперь в логах отображается **полный текст ответов** от Gemini API на всех уровнях:

### 1. Ответ от Gemini API (уровень сервиса)
```
💬 ОТВЕТ ОТ GEMINI API:
---START RESPONSE---
[Полный текст ответа от Gemini]
---END RESPONSE---
```

### 2. Предложения вопросов (JSON)
```
💡 ПРЕДЛОЖЕНИЯ ОТ GEMINI API:
---START SUGGESTIONS RESPONSE---
{
  "suggestions": [
    "Как получить API ключ для Gemini?",
    "Какие модели доступны и их различия?",
    "Каковы лимиты и ценообразование?",
    "Как начать работу с Gemini API?"
  ]
}
---END SUGGESTIONS RESPONSE---
```

### 3. Финальный ответ пользователю (уровень приложения)
```
💬 [APP] ФИНАЛЬНЫЙ ОТВЕТ ПОЛЬЗОВАТЕЛЮ:
---START FINAL RESPONSE---
[Тот же текст, который видит пользователь в интерфейсе]
---END FINAL RESPONSE---
```

## Пошаговая инструкция

### Шаг 1: Откройте Developer Tools
- **Chrome/Edge**: F12 или Ctrl+Shift+I
- **Firefox**: F12 или Ctrl+Shift+K
- **Safari**: Cmd+Option+I (Mac)

### Шаг 2: Перейдите на вкладку Console
- Кликните на вкладку "Console" в Developer Tools
- Очистите консоль (кнопка 🚫 или Ctrl+L)

### Шаг 3: Задайте вопрос в приложении
- Введите любой вопрос в чат
- Нажмите Enter или кнопку отправки

### Шаг 4: Наблюдайте за логами
Вы увидите последовательность логов:

```
🚀 [APP] Начало обработки сообщения пользователя
💬 Вопрос пользователя: Сколько стоит Gemini API?
📂 Активная группа: Gemini Docs Overview
🔗 Количество URL в активной группе: 9
✅ [APP] API ключ найден, продолжаем...
📤 [APP] Отправка запроса в Gemini Service...

🔍 [GEMINI SERVICE] Формирование запроса:
📝 Исходный вопрос пользователя: Сколько стоит Gemini API?
🔗 Количество URL для контекста: 9

📄 Полный промпт для Gemini API:
---START PROMPT---
Сколько стоит Gemini API?

Relevant URLs for context:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/pricing
[... остальные URL ...]
---END PROMPT---

🚀 Отправка запроса в Gemini API...

✅ Получен ответ от Gemini API
📊 Статус ответа: STOP
📝 Длина текста ответа: 1247 символов

💬 ОТВЕТ ОТ GEMINI API:
---START RESPONSE---
Gemini API предлагает несколько вариантов ценообразования:

**Бесплатный тариф:**
- До 15 запросов в минуту
- До 1,500 запросов в день
- До 1 миллиона токенов в месяц

**Платные тарифы:**
- Gemini 1.5 Flash: $0.075 за 1M входных токенов, $0.30 за 1M выходных токенов
- Gemini 1.5 Pro: $1.25 за 1M входных токенов, $5.00 за 1M выходных токенов

Источники: ai.google.dev/gemini-api/docs/pricing
---END RESPONSE---

✅ [APP] Получен ответ от Gemini Service
📝 Длина ответа: 1247 символов

💬 [APP] ФИНАЛЬНЫЙ ОТВЕТ ПОЛЬЗОВАТЕЛЮ:
---START FINAL RESPONSE---
[Тот же текст, что и выше]
---END FINAL RESPONSE---

✅ [APP] Сообщение обновлено в чате
🏁 [APP] Завершение обработки сообщения
```

## Полезные советы

### 1. Фильтрация логов
В консоли можете фильтровать по ключевым словам:
- `ОТВЕТ` - только ответы от API
- `ПРЕДЛОЖЕНИЯ` - только предложения вопросов
- `[APP]` - только логи уровня приложения
- `[GEMINI SERVICE]` - только логи сервиса

### 2. Копирование ответов
- Кликните правой кнопкой на лог с ответом
- Выберите "Copy" для копирования текста
- Можете сохранить ответ для анализа

### 3. Сравнение промпта и ответа
Теперь можете легко сравнить:
- Что отправили в API (секция `---START PROMPT---`)
- Что получили обратно (секция `---START RESPONSE---`)
- Что показали пользователю (секция `---START FINAL RESPONSE---`)

### 4. Отладка проблем
Если ответ не тот, что ожидали:
1. Проверьте промпт - возможно, вопрос сформулирован неточно
2. Посмотрите на использованные URL - возможно, нужны другие источники
3. Проверьте метаданные URL - возможно, некоторые страницы недоступны

## Пример реального использования

**Вопрос**: "Как настроить аутентификацию в Gemini API?"

**В логах увидите**:
1. **Промпт**: Ваш вопрос + список URL документации
2. **Ответ от API**: Подробная инструкция по настройке аутентификации
3. **Использованные URL**: Какие страницы реально помогли в ответе
4. **Финальный ответ**: То, что отображается пользователю

Это поможет понять:
- Насколько точно сформулирован вопрос
- Какие источники использует AI
- Качество получаемых ответов
- Возможности для оптимизации

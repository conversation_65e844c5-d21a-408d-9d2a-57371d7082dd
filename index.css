
body { 
  margin: 0; 
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  /* Default dark theme body bg and text set in App.tsx via Tailwind */
  transition: background-color 0.2s ease-out, color 0.2s ease-out;
}

/* Dark theme scrollbar */
.chat-container::-webkit-scrollbar { width: 6px; }
.chat-container::-webkit-scrollbar-thumb { background-color: #4A4A4A; border-radius: 3px; }
.chat-container::-webkit-scrollbar-track { background-color: #282828; }


/* Basic styling for rendered markdown - Dark Theme */
.prose h1 { font-size: 1.5em; font-weight: bold; margin-top: 0.8em; margin-bottom: 0.4em; color: #E2E2E2; }
.prose h2 { font-size: 1.25em; font-weight: bold; margin-top: 0.7em; margin-bottom: 0.35em; color: #E2E2E2; }
.prose h3 { font-size: 1.1em; font-weight: bold; margin-top: 0.6em; margin-bottom: 0.3em; color: #E2E2E2; }
.prose p { margin-bottom: 0.4em; line-height: 1.5; color: #E2E2E2; }
.prose ul, .prose ol { margin-left: 1.25em; margin-bottom: 0.4em; color: #E2E2E2; }
.prose li { margin-bottom: 0.2em; color: #E2E2E2; }
.prose strong { font-weight: bold; color: #E2E2E2; }
.prose em { font-style: italic; color: #E2E2E2; }
.prose code { /* background-color and color managed by highlight.js theme */ padding: 0.2em 0.4em; border-radius: 3px; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; }
.prose pre { /* background-color and color managed by highlight.js theme */ padding: 0.8em; border-radius: 4px; overflow-x: auto; margin-bottom: 0.5em; }
.prose pre code { background-color: transparent; padding: 0; font-size: 0.9em; color: inherit; }
.prose blockquote { border-left: 3px solid #4A4A4A; margin-left: 0; padding-left: 1em; color: #A8ABB4; margin-bottom: 0.5em;}
.prose a { color: #79B8FF; text-decoration: underline; }

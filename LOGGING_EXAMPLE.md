# Пример логирования в Chat with Docs

## Что добавлено в логирование

В файл `services/geminiService.ts` добавлено подробное логирование всех этапов работы с Gemini API:

### 1. Формирование запроса
- Исходный вопрос пользователя
- Количество URL для контекста
- Список всех URL
- Полный промпт, отправляемый в API
- Используемые инструменты

### 2. Ответ от API
- Статус ответа
- Длина текста ответа
- Метаданные обработанных URL
- Статус каждого URL

### 3. Обработка ошибок
- Тип ошибки
- Детали запроса при ошибке
- Специфичные сообщения для разных типов ошибок

## Пример логов в консоли

### Сценарий: Пользователь спрашивает "Сколько стоит Gemini API?"

```
🚀 [APP] Начало обработки сообщения пользователя
💬 Вопрос пользователя: Сколько стоит Gemini API?
📂 Активная группа: Gemini Docs Overview
🔗 Количество URL в активной группе: 9
✅ [APP] API ключ найден, продолжаем...
📤 [APP] Отправка запроса в Gemini Service...
🔗 URL для контекста: [
  "https://ai.google.dev/gemini-api/docs",
  "https://ai.google.dev/gemini-api/docs/quickstart",
  "https://ai.google.dev/gemini-api/docs/api-key",
  "https://ai.google.dev/gemini-api/docs/libraries",
  "https://ai.google.dev/gemini-api/docs/models",
  "https://ai.google.dev/gemini-api/docs/pricing",
  "https://ai.google.dev/gemini-api/docs/rate-limits",
  "https://ai.google.dev/gemini-api/docs/billing",
  "https://ai.google.dev/gemini-api/docs/changelog"
]

🔍 [GEMINI SERVICE] Формирование запроса:
📝 Исходный вопрос пользователя: Сколько стоит Gemini API?
🔗 Количество URL для контекста: 9
📋 Список URL для анализа:
   1. https://ai.google.dev/gemini-api/docs
   2. https://ai.google.dev/gemini-api/docs/quickstart
   3. https://ai.google.dev/gemini-api/docs/api-key
   4. https://ai.google.dev/gemini-api/docs/libraries
   5. https://ai.google.dev/gemini-api/docs/models
   6. https://ai.google.dev/gemini-api/docs/pricing
   7. https://ai.google.dev/gemini-api/docs/rate-limits
   8. https://ai.google.dev/gemini-api/docs/billing
   9. https://ai.google.dev/gemini-api/docs/changelog

📄 Полный промпт для Gemini API:
---START PROMPT---
Сколько стоит Gemini API?

Relevant URLs for context:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
https://ai.google.dev/gemini-api/docs/api-key
https://ai.google.dev/gemini-api/docs/libraries
https://ai.google.dev/gemini-api/docs/models
https://ai.google.dev/gemini-api/docs/pricing
https://ai.google.dev/gemini-api/docs/rate-limits
https://ai.google.dev/gemini-api/docs/billing
https://ai.google.dev/gemini-api/docs/changelog
---END PROMPT---

🛠️ Используемые инструменты: [
  {
    "urlContext": {}
  }
]

🚀 Отправка запроса в Gemini API...

✅ Получен ответ от Gemini API
📊 Статус ответа: STOP
📝 Длина текста ответа: 1247 символов

� ОТВЕТ ОТ GEMINI API:
---START RESPONSE---
Gemini API предлагает несколько вариантов ценообразования:

**Бесплатный тариф:**
- До 15 запросов в минуту
- До 1,500 запросов в день
- До 1 миллиона токенов в месяц

**Платные тарифы:**
- Gemini 1.5 Flash: $0.075 за 1M входных токенов, $0.30 за 1M выходных токенов
- Gemini 1.5 Pro: $1.25 за 1M входных токенов, $5.00 за 1M выходных токенов

Для получения API ключа перейдите в Google AI Studio и создайте новый проект. Бесплатный тариф подходит для тестирования и небольших проектов.

Источники: ai.google.dev/gemini-api/docs/pricing, ai.google.dev/gemini-api/docs/billing
---END RESPONSE---

�🔗 Найдены метаданные URL от Gemini API:
📋 Детали обработанных URL: [
  {
    "retrievedUrl": "https://ai.google.dev/gemini-api/docs/pricing",
    "urlRetrievalStatus": "SUCCESS"
  },
  {
    "retrievedUrl": "https://ai.google.dev/gemini-api/docs/billing",
    "urlRetrievalStatus": "SUCCESS"
  },
  {
    "retrievedUrl": "https://ai.google.dev/gemini-api/docs/rate-limits",
    "urlRetrievalStatus": "SUCCESS"
  }
]

📊 Успешно обработано URL: 3
   1. https://ai.google.dev/gemini-api/docs/pricing - статус: SUCCESS
   2. https://ai.google.dev/gemini-api/docs/billing - статус: SUCCESS
   3. https://ai.google.dev/gemini-api/docs/rate-limits - статус: SUCCESS

🎯 Формирование финального ответа...

✅ [APP] Получен ответ от Gemini Service
📝 Длина ответа: 1247 символов
🔗 Метаданные URL: 3 элементов
📋 Использованные URL:
   1. https://ai.google.dev/gemini-api/docs/pricing - SUCCESS
   2. https://ai.google.dev/gemini-api/docs/billing - SUCCESS
   3. https://ai.google.dev/gemini-api/docs/rate-limits - SUCCESS

💬 [APP] ФИНАЛЬНЫЙ ОТВЕТ ПОЛЬЗОВАТЕЛЮ:
---START FINAL RESPONSE---
Gemini API предлагает несколько вариантов ценообразования:

**Бесплатный тариф:**
- До 15 запросов в минуту
- До 1,500 запросов в день
- До 1 миллиона токенов в месяц

**Платные тарифы:**
- Gemini 1.5 Flash: $0.075 за 1M входных токенов, $0.30 за 1M выходных токенов
- Gemini 1.5 Pro: $1.25 за 1M входных токенов, $5.00 за 1M выходных токенов

Для получения API ключа перейдите в Google AI Studio и создайте новый проект. Бесплатный тариф подходит для тестирования и небольших проектов.

Источники: ai.google.dev/gemini-api/docs/pricing, ai.google.dev/gemini-api/docs/billing
---END FINAL RESPONSE---

✅ [APP] Сообщение обновлено в чате
🏁 [APP] Завершение обработки сообщения
```

### Сценарий: Генерация предложений вопросов

```
💡 [SUGGESTIONS] Генерация предложений вопросов...
🔗 Количество URL для анализа: 9
📋 URL для генерации предложений:
   1. https://ai.google.dev/gemini-api/docs
   2. https://ai.google.dev/gemini-api/docs/quickstart
   3. https://ai.google.dev/gemini-api/docs/api-key
   4. https://ai.google.dev/gemini-api/docs/libraries
   5. https://ai.google.dev/gemini-api/docs/models
   6. https://ai.google.dev/gemini-api/docs/pricing
   7. https://ai.google.dev/gemini-api/docs/rate-limits
   8. https://ai.google.dev/gemini-api/docs/billing
   9. https://ai.google.dev/gemini-api/docs/changelog

📄 Промпт для генерации предложений:
---START SUGGESTIONS PROMPT---
Based on the content of the following documentation URLs, provide 3-4 concise and actionable questions a developer might ask to explore these documents. These questions should be suitable as quick-start prompts. Return ONLY a JSON object with a key "suggestions" containing an array of these question strings. For example: {"suggestions": ["What are the rate limits?", "How do I get an API key?", "Explain model X."]}

Relevant URLs:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
https://ai.google.dev/gemini-api/docs/api-key
https://ai.google.dev/gemini-api/docs/libraries
https://ai.google.dev/gemini-api/docs/models
https://ai.google.dev/gemini-api/docs/pricing
https://ai.google.dev/gemini-api/docs/rate-limits
https://ai.google.dev/gemini-api/docs/billing
https://ai.google.dev/gemini-api/docs/changelog
---END SUGGESTIONS PROMPT---

🚀 Отправка запроса для предложений...

✅ Получен ответ для предложений от Gemini API
📝 Сырой JSON ответ: {"suggestions": ["Как получить API ключ для Gemini?", "Какие модели доступны и их различия?", "Каковы лимиты и ценообразование?", "Как начать работу с Gemini API?"]}

💡 ПРЕДЛОЖЕНИЯ ОТ GEMINI API:
---START SUGGESTIONS RESPONSE---
{
  "suggestions": [
    "Как получить API ключ для Gemini?",
    "Какие модели доступны и их различия?",
    "Каковы лимиты и ценообразование?",
    "Как начать работу с Gemini API?"
  ]
}
---END SUGGESTIONS RESPONSE---

🎯 Возвращаем предложения вопросов
```

### Сценарий: Ошибка API

```
🚀 [APP] Начало обработки сообщения пользователя
💬 Вопрос пользователя: Что такое Gemini?
📂 Активная группа: Gemini Docs Overview
🔗 Количество URL в активной группе: 5
✅ [APP] API ключ найден, продолжаем...
📤 [APP] Отправка запроса в Gemini Service...
🔗 URL для контекста: [
  "https://ai.google.dev/gemini-api/docs",
  "https://ai.google.dev/gemini-api/docs/quickstart",
  "https://ai.google.dev/gemini-api/docs/api-key",
  "https://ai.google.dev/gemini-api/docs/libraries",
  "https://ai.google.dev/gemini-api/docs/models"
]

🔍 [GEMINI SERVICE] Формирование запроса:
📝 Исходный вопрос пользователя: Что такое Gemini?
🔗 Количество URL для контекста: 5
📋 Список URL для анализа:
   1. https://ai.google.dev/gemini-api/docs
   2. https://ai.google.dev/gemini-api/docs/quickstart
   3. https://ai.google.dev/gemini-api/docs/api-key
   4. https://ai.google.dev/gemini-api/docs/libraries
   5. https://ai.google.dev/gemini-api/docs/models

📄 Полный промпт для Gemini API:
---START PROMPT---
Что такое Gemini?

Relevant URLs for context:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
https://ai.google.dev/gemini-api/docs/api-key
https://ai.google.dev/gemini-api/docs/libraries
https://ai.google.dev/gemini-api/docs/models
---END PROMPT---

🛠️ Используемые инструменты: [{"urlContext": {}}]
🚀 Отправка запроса в Gemini API...

❌ Ошибка при вызове Gemini API: GoogleGenAIError: API key not valid
🔍 Детали запроса при ошибке:
   - Модель: gemini-2.5-flash
   - Количество URL: 5
   - Длина промпта: 234 символов
🔍 Тип ошибки: GoogleGenAIError
📝 Сообщение ошибки: API key not valid
🔑 Проблема с API ключом

❌ [APP] Ошибка при получении ответа: Invalid API Key. Please check your GEMINI_API_KEY environment variable.
🏁 [APP] Завершение обработки сообщения
```

## Преимущества добавленного логирования

### 1. **Отладка**
- Видно точно какой промпт отправляется в API
- Можно проследить весь процесс обработки
- Легко найти причину ошибок

### 2. **Мониторинг**
- Отслеживание использования API
- Контроль количества обрабатываемых URL
- Статистика успешности запросов

### 3. **Оптимизация**
- Анализ длины промптов
- Понимание какие URL действительно используются
- Оценка эффективности запросов

### 4. **Пользовательский опыт**
- Понимание почему некоторые запросы медленные
- Диагностика проблем с конкретными URL
- Улучшение качества ответов

## Как использовать логи

1. **Откройте Developer Tools** в браузере (F12)
2. **Перейдите на вкладку Console**
3. **Задайте вопрос** в приложении
4. **Наблюдайте** за логами в реальном времени

Логи помогут понять:
- Какие URL действительно обрабатывает Gemini
- Почему некоторые запросы не работают
- Как оптимизировать промпты для лучших результатов

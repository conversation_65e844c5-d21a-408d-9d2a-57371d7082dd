# Примеры использования Chat with Docs

## Как работает приложение

Приложение позволяет задавать вопросы о содержимом веб-страниц через Gemini AI API. Вместо прямого скрапинга страниц, оно использует встроенную функциональность Gemini для получения и анализа контента.

## Практические примеры

### Пример 1: Вопрос о ценах на Gemini API

**Группа URL:** "Gemini Docs Overview"
**Активные URL:** 
- https://ai.google.dev/gemini-api/docs/pricing
- https://ai.google.dev/gemini-api/docs/billing
- https://ai.google.dev/gemini-api/docs/rate-limits

**Вопрос пользователя:**
```
Сколько стоит использование Gemini API? Есть ли бесплатный тариф?
```

**Что происходит внутри:**
1. Приложение формирует промпт:
```
Сколько стоит использование Gemini API? Есть ли бесплатный тариф?

Relevant URLs for context:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
https://ai.google.dev/gemini-api/docs/api-key
https://ai.google.dev/gemini-api/docs/libraries
https://ai.google.dev/gemini-api/docs/models
https://ai.google.dev/gemini-api/docs/pricing
https://ai.google.dev/gemini-api/docs/rate-limits
https://ai.google.dev/gemini-api/docs/billing
https://ai.google.dev/gemini-api/docs/changelog
```

2. Отправляет в Gemini API с инструментом `urlContext`
3. Gemini автоматически получает содержимое всех URL и анализирует их

**Ожидаемый ответ:**
```
Gemini API предлагает несколько вариантов ценообразования:

**Бесплатный тариф:**
- До 15 запросов в минуту
- До 1,500 запросов в день
- До 1 миллиона токенов в месяц

**Платные тарифы:**
- Gemini 1.5 Flash: $0.075 за 1M входных токенов, $0.30 за 1M выходных токенов
- Gemini 1.5 Pro: $1.25 за 1M входных токенов, $5.00 за 1M выходных токенов

Источники: ai.google.dev/gemini-api/docs/pricing, ai.google.dev/gemini-api/docs/billing
```

### Пример 2: Вопрос о возможностях модели

**Группа URL:** "Model Capabilities"
**Активные URL:**
- https://ai.google.dev/gemini-api/docs/text-generation
- https://ai.google.dev/gemini-api/docs/image-generation
- https://ai.google.dev/gemini-api/docs/video
- https://ai.google.dev/gemini-api/docs/function-calling

**Вопрос пользователя:**
```
Может ли Gemini генерировать изображения и работать с видео?
```

**Что происходит внутри:**
1. Gemini API получает контент со страниц о возможностях модели
2. Анализирует информацию о генерации изображений и работе с видео
3. Формирует ответ на основе актуального содержимого документации

**Ожидаемый ответ:**
```
Да, Gemini имеет расширенные мультимодальные возможности:

**Генерация изображений:**
- Gemini может создавать изображения по текстовому описанию
- Поддерживает различные стили и форматы
- Интеграция через API для программного создания изображений

**Работа с видео:**
- Анализ и понимание видеоконтента
- Извлечение информации из видеофайлов
- Генерация описаний видео
- Ответы на вопросы о содержимом видео

**Дополнительные возможности:**
- Обработка аудио
- Анализ документов
- Function calling для интеграции с внешними системами

Источники: ai.google.dev/gemini-api/docs/image-generation, ai.google.dev/gemini-api/docs/video
```

### Пример 3: Технический вопрос о реализации

**Вопрос пользователя:**
```
Как настроить аутентификацию и получить API ключ для Gemini?
```

**Что происходит внутри:**
1. Gemini API обращается к странице https://ai.google.dev/gemini-api/docs/api-key
2. Получает актуальную информацию о процессе получения ключа
3. Формирует пошаговую инструкцию

**Ожидаемый ответ:**
```
Для получения API ключа Gemini выполните следующие шаги:

1. **Перейдите в Google AI Studio:**
   - Откройте https://aistudio.google.com/
   - Войдите в свой Google аккаунт

2. **Создайте API ключ:**
   - Нажмите "Get API key" в левом меню
   - Выберите "Create API key"
   - Выберите существующий проект или создайте новый

3. **Настройте в приложении:**
   ```bash
   export API_KEY="your-api-key-here"
   ```

4. **Безопасность:**
   - Не публикуйте ключ в коде
   - Используйте переменные окружения
   - Настройте ограничения для ключа в Google Cloud Console

Источник: ai.google.dev/gemini-api/docs/api-key
```

## Преимущества такого подхода

1. **Актуальность данных:** Gemini получает свежую информацию при каждом запросе
2. **Нет проблем с CORS:** API Google обходит ограничения браузера
3. **Интеллектуальный анализ:** ИИ понимает контекст и структуру документации
4. **Мультиязычность:** Может отвечать на русском, даже если документация на английском

## Ограничения

1. **Стоимость:** Каждый запрос тратит токены API
2. **Зависимость от API:** Без доступа к Gemini функция не работает
3. **Ограничения по URL:** Максимум 20 URL на группу
4. **Время ответа:** Зависит от скорости обработки Gemini API

## Пример 4: Автоматические предложения вопросов

**Что происходит при выборе группы URL:**

Когда пользователь выбирает группу "Gemini Docs Overview", приложение автоматически генерирует предложения вопросов.

**Промпт для генерации предложений:**
```
Based on the content of the following documentation URLs, provide 3-4 concise and actionable questions a developer might ask to explore these documents. These questions should be suitable as quick-start prompts. Return ONLY a JSON object with a key "suggestions" containing an array of these question strings.

Relevant URLs:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
https://ai.google.dev/gemini-api/docs/api-key
https://ai.google.dev/gemini-api/docs/libraries
https://ai.google.dev/gemini-api/docs/models
https://ai.google.dev/gemini-api/docs/pricing
https://ai.google.dev/gemini-api/docs/rate-limits
https://ai.google.dev/gemini-api/docs/billing
https://ai.google.dev/gemini-api/docs/changelog
```

**Ожидаемый ответ от Gemini:**
```json
{
  "suggestions": [
    "Как получить API ключ для Gemini?",
    "Какие модели доступны и их различия?",
    "Каковы лимиты и ценообразование?",
    "Как начать работу с Gemini API?"
  ]
}
```

**Отображение в интерфейсе:**
Эти предложения появляются как кликабельные кнопки под приветственным сообщением, позволяя пользователю быстро начать исследование документации.

## Пример 5: Полный цикл взаимодействия

**Шаг 1:** Пользователь открывает приложение
- Отображается: "Welcome to Documentation Browser! You're currently browsing content from: 'Gemini Docs Overview'"
- Появляются автоматические предложения вопросов

**Шаг 2:** Пользователь кликает на предложение "Какие модели доступны и их различия?"

**Шаг 3:** Приложение отправляет запрос:
```
Какие модели доступны и их различия?

Relevant URLs for context:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
https://ai.google.dev/gemini-api/docs/api-key
https://ai.google.dev/gemini-api/docs/libraries
https://ai.google.dev/gemini-api/docs/models
https://ai.google.dev/gemini-api/docs/pricing
https://ai.google.dev/gemini-api/docs/rate-limits
https://ai.google.dev/gemini-api/docs/billing
https://ai.google.dev/gemini-api/docs/changelog
```

**Шаг 4:** Gemini API:
1. Получает содержимое всех указанных URL
2. Анализирует информацию о моделях
3. Формирует структурированный ответ

**Шаг 5:** Пользователь получает ответ:
```
Доступны следующие модели Gemini:

**Gemini 1.5 Flash:**
- Быстрая и эффективная модель
- Оптимизирована для высокой производительности
- Подходит для большинства задач
- Стоимость: $0.075/$0.30 за 1M токенов (вход/выход)

**Gemini 1.5 Pro:**
- Наиболее мощная модель
- Лучшее качество для сложных задач
- Расширенные возможности рассуждения
- Стоимость: $1.25/$5.00 за 1M токенов (вход/выход)

**Gemini 2.5 Flash:**
- Новейшая версия Flash модели
- Улучшенная производительность
- Поддержка мультимодальности

**Основные различия:**
- **Скорость:** Flash модели быстрее
- **Качество:** Pro модели дают более точные результаты
- **Стоимость:** Flash модели дешевле
- **Применение:** Flash для продакшена, Pro для сложных задач

Источники: ai.google.dev/gemini-api/docs/models, ai.google.dev/gemini-api/docs/pricing
```

**Шаг 6:** В интерфейсе также отображается информация об использованных URL:
- ✅ ai.google.dev/gemini-api/docs/models (успешно получен)
- ✅ ai.google.dev/gemini-api/docs/pricing (успешно получен)
- ⚠️ ai.google.dev/gemini-api/docs/quickstart (частично доступен)

## Технические детали

**Модель:** gemini-2.5-flash
**Инструмент:** urlContext для автоматического получения контента
**Безопасность:** Настроены фильтры контента для блокировки нежелательного содержимого
**Формат ответа:** Для предложений используется `responseMimeType: "application/json"`
**Обработка ошибок:** Детальная обработка ошибок API и сетевых проблем

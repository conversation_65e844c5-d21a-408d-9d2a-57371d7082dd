/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/


import { GoogleGenAI, GenerateContentResponse, Tool, HarmCategory, HarmBlockThreshold, Content } from "@google/genai";
import { UrlContextMetadataItem } from '../types';

// IMPORTANT: The API key MUST be set as an environment variable `process.env.API_KEY`
const API_KEY = process.env.API_KEY;

let ai: GoogleGenAI;

// Model supporting URL context, consistent with user examples and documentation.
const MODEL_NAME = "gemini-2.5-flash"; 

const getAiInstance = (): GoogleGenAI => {
  if (!API_KEY) {
    console.error("API_KEY is not set in environment variables. Please set process.env.API_KEY.");
    throw new Error("Gemini API Key not configured. Set process.env.API_KEY.");
  }
  if (!ai) {
    ai = new GoogleGenAI({ apiKey: API_KEY });
  }
  return ai;
};

const safetySettings = [
  { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
  { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
  { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
  { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
];

interface GeminiResponse {
  text: string;
  urlContextMetadata?: UrlContextMetadataItem[];
}

export const generateContentWithUrlContext = async (
  prompt: string,
  urls: string[]
): Promise<GeminiResponse> => {
  const currentAi = getAiInstance();

  console.log('🔍 [GEMINI SERVICE] Формирование запроса:');
  console.log('📝 Исходный вопрос пользователя:', prompt);
  console.log('🔗 Количество URL для контекста:', urls.length);

  let fullPrompt = prompt;
  if (urls.length > 0) {
    const urlList = urls.join('\n');
    fullPrompt = `${prompt}\n\nRelevant URLs for context:\n${urlList}`;
    console.log('📋 Список URL для анализа:');
    urls.forEach((url, index) => {
      console.log(`   ${index + 1}. ${url}`);
    });
  }

  console.log('📄 Полный промпт для Gemini API:');
  console.log('---START PROMPT---');
  console.log(fullPrompt);
  console.log('---END PROMPT---');

  const tools: Tool[] = [{ urlContext: {} }];
  const contents: Content[] = [{ role: "user", parts: [{ text: fullPrompt }] }];

  console.log('🛠️ Используемые инструменты:', JSON.stringify(tools, null, 2));
  console.log('🚀 Отправка запроса в Gemini API...');

  try {
    const response: GenerateContentResponse = await currentAi.models.generateContent({
      model: MODEL_NAME,
      contents: contents,
      config: {
        tools: tools,
        safetySettings: safetySettings,
      },
    });

    console.log('✅ Получен ответ от Gemini API');
    console.log('📊 Статус ответа:', response.candidates?.[0]?.finishReason || 'unknown');

    const text = response.text;
    console.log('📝 Длина текста ответа:', text?.length || 0, 'символов');

    const candidate = response.candidates?.[0];
    let extractedUrlContextMetadata: UrlContextMetadataItem[] | undefined = undefined;

    if (candidate && candidate.urlContextMetadata && candidate.urlContextMetadata.urlMetadata) {
      console.log("🔗 Найдены метаданные URL от Gemini API:");
      console.log("📋 Детали обработанных URL:", JSON.stringify(candidate.urlContextMetadata.urlMetadata, null, 2));

      // Assuming SDK converts snake_case to camelCase, UrlContextMetadataItem type (now camelCase) should match items in urlMetadata.
      extractedUrlContextMetadata = candidate.urlContextMetadata.urlMetadata as UrlContextMetadataItem[];

      console.log(`📊 Успешно обработано URL: ${extractedUrlContextMetadata.length}`);
      extractedUrlContextMetadata.forEach((urlMeta, index) => {
        console.log(`   ${index + 1}. ${urlMeta.retrievedUrl} - статус: ${urlMeta.urlRetrievalStatus}`);
      });
    } else if (candidate && candidate.urlContextMetadata) {
      // This case implies urlContextMetadata exists but urlMetadata field might be missing or empty.
      console.warn("⚠️ urlContextMetadata присутствует, но поле 'urlMetadata' отсутствует или пустое:", JSON.stringify(candidate.urlContextMetadata, null, 2));
    } else {
      console.log("ℹ️ Метаданные URL не найдены в ответе Gemini API");
    }

    console.log('🎯 Формирование финального ответа...');
    return { text, urlContextMetadata: extractedUrlContextMetadata };

  } catch (error) {
    console.error("❌ Ошибка при вызове Gemini API:", error);
    console.log("🔍 Детали запроса при ошибке:");
    console.log("   - Модель:", MODEL_NAME);
    console.log("   - Количество URL:", urls.length);
    console.log("   - Длина промпта:", fullPrompt.length, "символов");

    if (error instanceof Error) {
      const googleError = error as any;
      console.log("🔍 Тип ошибки:", googleError.type || 'Unknown');
      console.log("📝 Сообщение ошибки:", googleError.message || 'No message');

      if (googleError.message && googleError.message.includes("API key not valid")) {
        console.error("🔑 Проблема с API ключом");
        throw new Error("Invalid API Key. Please check your GEMINI_API_KEY environment variable.");
      }
      if (googleError.message && googleError.message.includes("quota")) {
        console.error("📊 Превышена квота API");
        throw new Error("API quota exceeded. Please check your Gemini API quota.");
      }
      if (googleError.type === 'GoogleGenAIError' && googleError.message) {
        console.error("🤖 Ошибка Gemini API:", googleError.message);
        throw new Error(`Gemini API Error: ${googleError.message}`);
      }
      console.error("⚠️ Общая ошибка AI:", error.message);
      throw new Error(`Failed to get response from AI: ${error.message}`);
    }
    console.error("❓ Неизвестная ошибка");
    throw new Error("Failed to get response from AI due to an unknown error.");
  }
};

// This function now aims to get a JSON array of string suggestions.
export const getInitialSuggestions = async (urls: string[]): Promise<GeminiResponse> => {
  console.log('💡 [SUGGESTIONS] Генерация предложений вопросов...');
  console.log('🔗 Количество URL для анализа:', urls.length);

  if (urls.length === 0) {
    console.log('⚠️ Нет URL для анализа, возвращаем заглушку');
    // This case should ideally be handled by the caller, but as a fallback:
    return { text: JSON.stringify({ suggestions: ["Add some URLs to get topic suggestions."] }) };
  }

  const currentAi = getAiInstance();
  const urlList = urls.join('\n');

  console.log('📋 URL для генерации предложений:');
  urls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });
  
  // Prompt updated to request JSON output of short questions
  const promptText = `Based on the content of the following documentation URLs, provide 3-4 concise and actionable questions a developer might ask to explore these documents. These questions should be suitable as quick-start prompts. Return ONLY a JSON object with a key "suggestions" containing an array of these question strings. For example: {"suggestions": ["What are the rate limits?", "How do I get an API key?", "Explain model X."]}

Relevant URLs:
${urlList}`;

  const contents: Content[] = [{ role: "user", parts: [{ text: promptText }] }];

  console.log('📄 Промпт для генерации предложений:');
  console.log('---START SUGGESTIONS PROMPT---');
  console.log(promptText);
  console.log('---END SUGGESTIONS PROMPT---');
  console.log('🚀 Отправка запроса для предложений...');

  try {
    const response: GenerateContentResponse = await currentAi.models.generateContent({
      model: MODEL_NAME,
      contents: contents,
      config: {
        safetySettings: safetySettings,
        responseMimeType: "application/json", // Request JSON output
      },
    });

    console.log('✅ Получен ответ для предложений от Gemini API');
    const text = response.text; // This should be the JSON string
    console.log('📝 Сырой JSON ответ:', text);

    // urlContextMetadata is not expected here because tools cannot be used with responseMimeType: "application/json"
    // const urlContextMetadata = response.candidates?.[0]?.urlContextMetadata?.urlMetadata as UrlContextMetadataItem[] | undefined;

    console.log('🎯 Возвращаем предложения вопросов');
    return { text /*, urlContextMetadata: undefined */ }; // Explicitly undefined or not included

  } catch (error) {
    console.error("Error calling Gemini API for initial suggestions:", error);
     if (error instanceof Error) {
      const googleError = error as any; 
      if (googleError.message && googleError.message.includes("API key not valid")) {
         throw new Error("Invalid API Key for suggestions. Please check your GEMINI_API_KEY environment variable.");
      }
      // Check for the specific error message and re-throw a more informative one if needed
      if (googleError.message && googleError.message.includes("Tool use with a response mime type: 'application/json' is unsupported")) {
        throw new Error("Configuration error: Cannot use tools with JSON response type for suggestions. This should be fixed in the code.");
      }
      throw new Error(`Failed to get initial suggestions from AI: ${error.message}`);
    }
    throw new Error("Failed to get initial suggestions from AI due to an unknown error.");
  }
};
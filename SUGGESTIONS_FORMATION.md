# Как формируются предложения (suggestions) на первой странице

## Полный цикл формирования предложений

### 1. Инициализация приложения

При загрузке приложения происходит следующее:

```typescript
// App.tsx - строки 42-49
const INITIAL_URL_GROUPS: URLGroup[] = [
  { id: 'gemini-overview', name: 'Gemini Docs Overview', urls: GEMINI_DOCS_URLS },
  { id: 'model-capabilities', name: 'Model Capabilities', urls: MODEL_CAPABILITIES_URLS },
];

const [activeUrlGroupId, setActiveUrlGroupId] = useState<string>(INITIAL_URL_GROUPS[0].id);
const [initialQuerySuggestions, setInitialQuerySuggestions] = useState<string[]>([]);
```

**По умолчанию активна группа "Gemini Docs Overview"** с 9 URL:
- https://ai.google.dev/gemini-api/docs
- https://ai.google.dev/gemini-api/docs/quickstart
- https://ai.google.dev/gemini-api/docs/api-key
- https://ai.google.dev/gemini-api/docs/libraries
- https://ai.google.dev/gemini-api/docs/models
- https://ai.google.dev/gemini-api/docs/pricing
- https://ai.google.dev/gemini-api/docs/rate-limits
- https://ai.google.dev/gemini-api/docs/billing
- https://ai.google.dev/gemini-api/docs/changelog

### 2. Триггер генерации предложений

```typescript
// App.tsx - строки 119-126
useEffect(() => {
  if (currentUrlsForChat.length > 0 && process.env.API_KEY) { 
      fetchAndSetInitialSuggestions(currentUrlsForChat);
  } else {
      setInitialQuerySuggestions([]); 
  }
}, [currentUrlsForChat, fetchAndSetInitialSuggestions]);
```

**Условия для генерации:**
- ✅ Есть URL в активной группе (`currentUrlsForChat.length > 0`)
- ✅ Настроен API ключ (`process.env.API_KEY`)

### 3. Функция генерации предложений

```typescript
// App.tsx - строки 78-117
const fetchAndSetInitialSuggestions = useCallback(async (currentUrls: string[]) => {
  if (currentUrls.length === 0) {
    setInitialQuerySuggestions([]);
    return;
  }
    
  setIsFetchingSuggestions(true);  // Показать индикатор загрузки
  setInitialQuerySuggestions([]);  // Очистить старые предложения

  try {
    const response = await getInitialSuggestions(currentUrls);
    // Парсинг JSON ответа...
    setInitialQuerySuggestions(suggestionsArray.slice(0, 4)); // Максимум 4 предложения
  } catch (e: any) {
    // Обработка ошибок...
  } finally {
    setIsFetchingSuggestions(false);  // Скрыть индикатор загрузки
  }
}, []);
```

### 4. Вызов Gemini API для предложений

```typescript
// services/geminiService.ts - строки 150-222
export const getInitialSuggestions = async (urls: string[]): Promise<GeminiResponse> => {
  const promptText = `Based on the content of the following documentation URLs, provide 3-4 concise and actionable questions a developer might ask to explore these documents. These questions should be suitable as quick-start prompts. Return ONLY a JSON object with a key "suggestions" containing an array of these question strings.

Relevant URLs:
${urlList}`;

  const response = await currentAi.models.generateContent({
    model: "gemini-2.5-flash",
    contents: [{ role: "user", parts: [{ text: promptText }] }],
    config: {
      safetySettings: safetySettings,
      responseMimeType: "application/json", // Важно: запрашиваем JSON
    },
  });

  return { text: response.text };
};
```

**Ключевые особенности:**
- 🚫 **НЕ использует urlContext tool** (несовместимо с JSON режимом)
- ✅ **Запрашивает JSON формат** (`responseMimeType: "application/json"`)
- ✅ **Отправляет список всех URL** для анализа

### 5. Обработка ответа от API

```typescript
// App.tsx - строки 88-110
const response = await getInitialSuggestions(currentUrls);
let suggestionsArray: string[] = [];

if (response.text) {
  try {
    let jsonStr = response.text.trim();
    
    // Удаление markdown code fences если есть
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s; 
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const parsed = JSON.parse(jsonStr);
    if (parsed && Array.isArray(parsed.suggestions)) {
      suggestionsArray = parsed.suggestions.filter((s: unknown) => typeof s === 'string');
    }
  } catch (parseError) {
    // Обработка ошибок парсинга...
  }
}

setInitialQuerySuggestions(suggestionsArray.slice(0, 4)); // Максимум 4
```

### 6. Отображение в интерфейсе

```typescript
// components/ChatInterface.tsx - строки 48, 91-100
const showSuggestions = initialQuerySuggestions && 
  initialQuerySuggestions.length > 0 && 
  messages.filter(m => m.sender !== MessageSender.SYSTEM).length <= 1;

{showSuggestions && onSuggestedQueryClick && (
  <div className="my-3 px-1">
    <p className="text-xs text-[#A8ABB4] mb-1.5 font-medium">Or try one of these: </p>
    <div className="flex flex-wrap gap-1.5">
      {initialQuerySuggestions.map((suggestion, index) => (
        <button
          key={index}
          onClick={() => onSuggestedQueryClick(suggestion)}
          className="bg-[#79B8FF]/10 text-[#79B8FF] px-2.5 py-1 rounded-full text-xs hover:bg-[#79B8FF]/20 transition-colors shadow-sm"
        >
          {suggestion}
        </button>
      ))}
    </div>
  </div>
)}
```

**Условия показа предложений:**
- ✅ Есть предложения (`initialQuerySuggestions.length > 0`)
- ✅ Нет пользовательских сообщений (только системные)
- ✅ Передана функция обработки клика (`onSuggestedQueryClick`)

## Пример полного цикла в логах

```
💡 [SUGGESTIONS] Генерация предложений вопросов...
🔗 Количество URL для анализа: 9
📋 URL для генерации предложений:
   1. https://ai.google.dev/gemini-api/docs
   2. https://ai.google.dev/gemini-api/docs/quickstart
   3. https://ai.google.dev/gemini-api/docs/api-key
   ...

📄 Промпт для генерации предложений:
---START SUGGESTIONS PROMPT---
Based on the content of the following documentation URLs, provide 3-4 concise and actionable questions a developer might ask to explore these documents...

Relevant URLs:
https://ai.google.dev/gemini-api/docs
https://ai.google.dev/gemini-api/docs/quickstart
...
---END SUGGESTIONS PROMPT---

🚀 Отправка запроса для предложений...

✅ Получен ответ для предложений от Gemini API
📝 Сырой JSON ответ: {"suggestions": ["Как получить API ключ для Gemini?", "Какие модели доступны и их различия?", "Каковы лимиты и ценообразование?", "Как начать работу с Gemini API?"]}

💡 ПРЕДЛОЖЕНИЯ ОТ GEMINI API:
---START SUGGESTIONS RESPONSE---
{
  "suggestions": [
    "Как получить API ключ для Gemini?",
    "Какие модели доступны и их различия?", 
    "Каковы лимиты и ценообразование?",
    "Как начать работу с Gemini API?"
  ]
}
---END SUGGESTIONS RESPONSE---

🎯 Возвращаем предложения вопросов
```

## Ключевые отличия от обычных запросов

| Аспект | Предложения | Обычные запросы |
|--------|-------------|-----------------|
| **Инструменты** | Без urlContext | С urlContext |
| **Формат ответа** | JSON | Обычный текст |
| **Получение контента** | НЕТ (только URL в промпте) | ДА (автоматически) |
| **Цель** | Генерация вопросов | Ответы на вопросы |
| **Когда вызывается** | При загрузке/смене группы | При отправке сообщения |

## Почему предложения могут не появиться

1. **Нет API ключа** - `process.env.API_KEY` не настроен
2. **Нет URL** - активная группа пустая
3. **Ошибка API** - проблемы с Gemini API
4. **Ошибка парсинга** - некорректный JSON от API
5. **Уже есть сообщения** - пользователь уже начал чат
